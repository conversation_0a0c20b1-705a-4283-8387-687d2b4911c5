/* Dashboard Page Specific Styles */

.postbox-container.left-column {
    width: 48%;
    float: left;
}

.postbox-container.right-column {
    width: 48%;
    float: right;
}

#git-status-output {
    margin-top: 20px;
}

.git-status-overview .status-item {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.git-status-overview .status-item:last-child {
    border-bottom: none;
}

.branch-name {
    background: #0073aa;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.commit-count,
.changes-count {
    font-weight: bold;
    color: #0073aa;
}

.git-file-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    padding: 5px 0;
}

.file-status {
    display: inline-block;
    width: 20px;
    text-align: center;
    font-weight: bold;
    margin-right: 10px;
    font-size: 12px;
}

.status-m {
    color: #d63638;
}

/* Modified */
.status-a {
    color: #00a32a;
}

/* Added */
.status-d {
    color: #d63638;
}

/* Deleted */
.status-r {
    color: #dba617;
}

/* Renamed */

.commit-item {
    margin-bottom: 15px;
    padding: 10px;
    background: #f9f9f9;
    border-left: 3px solid #0073aa;
}

.commit-hash {
    font-family: monospace;
    margin-bottom: 5px;
}

.commit-message {
    font-weight: bold;
    margin-bottom: 5px;
}

.commit-meta {
    font-size: 12px;
    color: #666;
}

.commit-author {
    margin-right: 15px;
}

.remote-item {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.remote-item:last-child {
    border-bottom: none;
}

.git-quick-actions .button {
    margin-right: 5px;
    margin-bottom: 5px;
}

.git-quick-actions .dashicons {
    margin-right: 5px;
}
