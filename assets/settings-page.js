/**
 * Git Manager Settings Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const SettingsPage = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            $('#test-git-path').on('click', this.handleTestGitPath);
            $('#test-connection').on('click', this.handleTestConnection);
            $('#reset-git-only').on('click', this.handleResetGitOnly);
            $('#reset-all-data').on('click', this.handleResetAllData);
        },

        handleTestGitPath: function() {
            var button = $(this);
            var originalText = button.text();
            button.prop('disabled', true).text('Testing...');

            var gitPath = $('input[name="git_path"]').val();

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        button.after('<span class="test-result success">✓ ' + response.data.version + '</span>');
                    } else {
                        button.after('<span class="test-result error">✗ ' + response.data + '</span>');
                    }
                    setTimeout(function() {
                        $('.test-result').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 5000);
                })
                .always(function() {
                    button.prop('disabled', false).text(originalText);
                });
        },

        handleTestConnection: function() {
            var button = $(this);
            button.prop('disabled', true).text('Testing...');

            var gitPath = $('input[name="git_path"]').val();

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#action-output').html('✓ Git connection successful!\nVersion: ' + response.data.version);
                    } else {
                        $('#action-output').html('✗ Git connection failed: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Connection');
                });
        },

        handleResetGitOnly: function() {
            var confirmMessage = 'WARNING: This will permanently remove the Git repository (.git directory) and basic Git configuration.\n\n' +
                              'Your plugin settings will be preserved, but all Git history will be lost.\n\n' +
                              'Type "RESET_REPOSITORY" to confirm:';

            var confirmation = prompt(confirmMessage);

            if (confirmation === 'RESET_REPOSITORY') {
                SettingsPage.performReset(false);
            } else if (confirmation !== null) {
                alert('Reset cancelled. You must type "RESET_REPOSITORY" exactly to confirm.');
            }
        },

        handleResetAllData: function() {
            var confirmMessage = 'DANGER: This will permanently remove ALL plugin data including:\n' +
                              '• Git repository (.git directory)\n' +
                              '• All plugin settings and configuration\n' +
                              '• .gitignore file\n' +
                              '• Plugin will be reset to initial state\n\n' +
                              'This action cannot be undone!\n\n' +
                              'Type "RESET_REPOSITORY" to confirm:';

            var confirmation = prompt(confirmMessage);

            if (confirmation === 'RESET_REPOSITORY') {
                SettingsPage.performReset(true);
            } else if (confirmation !== null) {
                alert('Reset cancelled. You must type "RESET_REPOSITORY" exactly to confirm.');
            }
        },

        performReset: function(clearAllData) {
            var loadingMessage = clearAllData ? 'Performing complete plugin reset...' : 'Resetting Git repository...';

            // Show loading state
            if (clearAllData) {
                $('#reset-all-data').prop('disabled', true).text('Resetting...');
            } else {
                $('#reset-git-only').prop('disabled', true).text('Resetting...');
            }

            $.post(ajaxurl, {
                action: 'git_reset_repository',
                confirm: 'RESET_REPOSITORY',
                clear_all_data: clearAllData.toString(),
                nonce: wpGitManager.nonce
            })
            .done(function(response) {
                if (response.success) {
                    alert('Success: ' + response.data);
                    if (clearAllData) {
                        // Redirect to setup page after complete reset
                        window.location.href = wpGitManager.admin_url + 'admin.php?page=wp-git-manager-setup';
                    } else {
                        // Reload the page to reflect changes
                        window.location.reload();
                    }
                } else {
                    alert('Error: ' + response.data);
                }
            })
            .fail(function(xhr, status, error) {
                alert('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')');
            })
            .always(function() {
                // Reset button states
                $('#reset-git-only').prop('disabled', false).text('Reset Git Repository');
                $('#reset-all-data').prop('disabled', false).text('Complete Reset');
            });
        }
    };

    // Initialize SettingsPage
    SettingsPage.init();
});
