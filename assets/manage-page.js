/**
 * Git Manager Manage Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const ManagePage = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            // Unbind any existing events first to prevent duplicates
            $(document).off('change', '.file-checkbox');
            $(document).off('click', '.git-stage-file');
            $(document).off('click', '.git-unstage-file');
            $(document).off('click', '.git-view-diff');
            $(document).off('click', '#stage-selected-btn');
            $(document).off('click', '#unstage-selected-btn');

            // File management
            $('#select-all-files').off('change').on('change', this.handleSelectAll);
            $(document).on('change', '.file-checkbox', this.handleFileCheckboxChange);
            $(document).on('click', '.git-stage-file', this.handleStageFile);
            $(document).on('click', '.git-unstage-file', this.handleUnstageFile);
            $(document).on('click', '.git-view-diff', this.handleViewDiff);
            $(document).on('click', '#stage-selected-btn', this.handleStageSelected);
            $(document).on('click', '#unstage-selected-btn', this.handleUnstageSelected);
            $('#stage-all').off('click').on('click', this.handleStageAll);
            $('#unstage-all').off('click').on('click', this.handleUnstageAll);
            $('#refresh-status').off('click').on('click', this.handleRefreshStatus);

            // Commit operations
            $('#create-commit').on('click', this.handleCreateCommit);

            // Branch management
            $('#create-branch').on('click', this.handleCreateBranch);
            $('#load-branches').on('click', this.handleLoadBranches);

            // Remote management
            $('#add-remote').on('click', this.handleAddRemote);
            $('#load-remotes').on('click', this.handleLoadRemotes);

            // History
            $('#load-history').on('click', this.handleLoadHistory);

            // Advanced operations
            $('#view-git-config').on('click', this.handleViewConfig);
            $('#git-gc').on('click', this.handleGarbageCollection);
            $('#git-fsck').on('click', this.handleFileSystemCheck);
            $('#reset-hard').on('click', this.handleResetHard);
            $('#clean-untracked').on('click', this.handleCleanUntracked);

            // Push/Pull buttons (manage page specific - avoid conflicts with admin bar)
            $('#manage-push-btn').on('click', this.handlePush);
            $('#manage-pull-btn').on('click', this.handlePull);
            $('#quick-commit-btn').on('click', this.handleQuickCommit);
        },

        handleSelectAll: function() {
            const isChecked = $(this).is(':checked');
            $('.file-checkbox').prop('checked', isChecked);
            ManagePage.updateBulkActionButtons();
        },

        handleFileCheckboxChange: function (e) {
            ManagePage.updateBulkActionButtons();

            // Update select-all checkbox state
            const totalCheckboxes = $('.file-checkbox').length;
            const checkedCheckboxes = $('.file-checkbox:checked').length;

            if (checkedCheckboxes === 0) {
                $('#select-all-files').prop('indeterminate', false).prop('checked', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                $('#select-all-files').prop('indeterminate', false).prop('checked', true);
            } else {
                $('#select-all-files').prop('indeterminate', true).prop('checked', false);
            }
        },

        updateBulkActionButtons: function () {
            const selectedFiles = $('.file-checkbox:checked');
            const selectedCount = selectedFiles.length;

            // Update button states
            $('#stage-selected-btn, #unstage-selected-btn').prop('disabled', selectedCount === 0);

            // Update count display
            if (selectedCount === 0) {
                $('#selected-count').text('');
            } else {
                $('#selected-count').text(selectedCount + ' file(s) selected');
            }
        },

        handleStageFile: function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            const fileId = $(this).data('file-id');
            const filePath = $(this).data('file-path');
            const fileIdentifier = fileId || filePath;

            if (!fileIdentifier) {
                ManagePage.showMessage('Error: No file identifier found', 'error');
                return;
            }

            ManagePage.stageFile(fileIdentifier);
        },

        handleUnstageFile: function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            const fileId = $(this).data('file-id');
            const filePath = $(this).data('file-path');
            const fileIdentifier = fileId || filePath;

            if (!fileIdentifier) {
                ManagePage.showMessage('Error: No file identifier found', 'error');
                return;
            }

            ManagePage.unstageFile(fileIdentifier);
        },

        handleViewDiff: function(e) {
            e.preventDefault();
            const fileId = $(this).data('file-id');
            const filePath = $(this).data('file-path');
            ManagePage.viewDiff(fileId || filePath); // Use file ID if available, fallback to path
        },

        handleStageAll: function(e) {
            e.preventDefault();
            ManagePage.stageAll();
        },

        handleUnstageAll: function(e) {
            e.preventDefault();
            ManagePage.unstageAll();
        },

        handleRefreshStatus: function(e) {
            e.preventDefault();
            ManagePage.refreshFileStatus();
        },

        handleCreateCommit: function(e) {
            e.preventDefault();
            const message = $('#commit-message').val();
            const autoStage = $('#auto-stage').is(':checked');

            if (!message.trim()) {
                alert('Please enter a commit message.');
                return;
            }

            ManagePage.createCommit(message, autoStage);
        },

        handleCreateBranch: function(e) {
            e.preventDefault();
            const branchName = $('#new-branch-name').val();

            if (!branchName.trim()) {
                alert('Please enter a branch name.');
                return;
            }

            // Check for unstaged changes before creating branch
            ManagePage.checkUnstagedChangesBeforeBranch(branchName);
        },

        handleLoadBranches: function(e) {
            e.preventDefault();
            ManagePage.loadBranches();
        },

        handleAddRemote: function(e) {
            e.preventDefault();
            const name = $('#remote-name').val();
            const url = $('#remote-url').val();

            if (!name.trim() || !url.trim()) {
                alert('Please enter both remote name and URL.');
                return;
            }

            ManagePage.addRemote(name, url);
        },

        handleLoadRemotes: function(e) {
            e.preventDefault();
            ManagePage.loadRemotes();
        },

        handleLoadHistory: function(e) {
            e.preventDefault();
            const limit = $('#history-limit').val();
            ManagePage.loadHistory(limit);
        },



        handleViewConfig: function(e) {
            e.preventDefault();
            ManagePage.executeGitCommand('config --list');
        },

        handleGarbageCollection: function(e) {
            e.preventDefault();
            if (confirm('Run garbage collection? This will optimize the repository.')) {
                ManagePage.executeGitCommand('gc');
            }
        },

        handleFileSystemCheck: function(e) {
            e.preventDefault();
            if (confirm('Run file system check? This will verify repository integrity.')) {
                ManagePage.executeGitCommand('fsck');
            }
        },

        handleResetHard: function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently discard all uncommitted changes. Are you sure?')) {
                ManagePage.executeGitCommand('reset --hard HEAD');
            }
        },

        handleCleanUntracked: function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently delete all untracked files. Are you sure?')) {
                ManagePage.executeGitCommand('clean -fd');
            }
        },

        handlePush: function(e) {
            e.preventDefault();
            GitUtils.git.push();
        },

        handlePull: function(e) {
            e.preventDefault();
            GitUtils.git.pull();
        },

        handleQuickCommit: function(e) {
            e.preventDefault();
            const message = prompt('Enter commit message:');
            if (message) {
                let autoStage = confirm('Stage all changes before committing?');
                ManagePage.createCommit(message, autoStage);
            }
        },

        handleStageSelected: function (e) {
            e.preventDefault();
            const selectedFiles = $('.file-checkbox:checked').map(function () {
                return $(this).val();
            }).get();

            if (selectedFiles.length === 0) {
                ManagePage.showMessage('No files selected', 'error');
                return;
            }

            ManagePage.stageSelectedFiles(selectedFiles);
        },

        handleUnstageSelected: function (e) {
            e.preventDefault();
            const selectedFiles = $('.file-checkbox:checked').map(function () {
                return $(this).val();
            }).get();

            if (selectedFiles.length === 0) {
                ManagePage.showMessage('No files selected', 'error');
                return;
            }

            ManagePage.unstageSelectedFiles(selectedFiles);
        },

        // API functions
        refreshFileStatus: function () {
            this.showLoading('Refreshing file status...');

            $.post(ajaxurl, {
                action: 'git_refresh_file_status',
                nonce: wpGitManager.nonce
            }, function (response) {
                ManagePage.hideLoading();
                if (response.success) {
                    // Update the file status container with new HTML
                    $('#git-file-status').html(response.data.html);

                    // Re-bind event handlers for the new elements
                    ManagePage.rebindFileEvents();

                    // Show success message
                    ManagePage.showMessage('File status refreshed successfully. Found ' + response.data.files_count + ' file(s) with changes.', 'success');
                } else {
                    ManagePage.showMessage('Error refreshing file status: ' + response.data, 'error');
                }
            }).fail(function (xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Refresh status AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        rebindFileEvents: function () {
            // Re-bind events for dynamically loaded file action buttons
            $('#select-all-files').off('change').on('change', this.handleSelectAll);
            $('.file-checkbox').off('change').on('change', this.handleFileCheckboxChange);
            $('.git-stage-file').off('click').on('click', this.handleStageFile);
            $('.git-unstage-file').off('click').on('click', this.handleUnstageFile);
            $('.git-view-diff').off('click').on('click', this.handleViewDiff);
            $('#stage-selected-btn').off('click').on('click', this.handleStageSelected);
            $('#unstage-selected-btn').off('click').on('click', this.handleUnstageSelected);
        },

        stageFile: function (fileIdentifier) {
            this.showLoading('Staging file...');

            // Determine if this is a file ID or file path
            const isFileId = fileIdentifier.startsWith('file_');
            const requestData = isFileId ? { file_id: fileIdentifier } : { file: fileIdentifier };
            requestData.action = 'git_stage_file';
            requestData.nonce = wpGitManager.nonce;

            $.post(ajaxurl, requestData, function (response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('File staged successfully', 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error staging file: ' + response.data, 'error');
                }
            }).fail(function (xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Stage file AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        unstageFile: function (fileIdentifier) {
            this.showLoading('Unstaging file...');

            // Determine if this is a file ID or file path
            const isFileId = fileIdentifier.startsWith('file_');
            const requestData = isFileId ? { file_id: fileIdentifier } : { file: fileIdentifier };
            requestData.action = 'git_unstage_file';
            requestData.nonce = wpGitManager.nonce;

            $.post(ajaxurl, requestData, function (response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('File unstaged successfully', 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error unstaging file: ' + response.data, 'error');
                }
            }).fail(function (xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Unstage file AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        stageSelectedFiles: function (files) {
            this.showLoading('Staging selected files...');

            $.post(ajaxurl, {
                action: 'git_stage_selected_files',
                files: files,
                nonce: wpGitManager.nonce
            }, function (response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage(response.data.message, 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error staging files: ' + response.data, 'error');
                }
            }).fail(function (xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Stage selected files AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        unstageSelectedFiles: function (files) {
            this.showLoading('Unstaging selected files...');

            $.post(ajaxurl, {
                action: 'git_unstage_selected_files',
                files: files,
                nonce: wpGitManager.nonce
            }, function (response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage(response.data.message, 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error unstaging files: ' + response.data, 'error');
                }
            }).fail(function (xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Unstage selected files AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        stageAll: function() {
            this.showLoading('Staging all files...');

            $.post(ajaxurl, {
                action: 'git_stage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('All files staged successfully', 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error staging files: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Stage all AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        unstageAll: function() {
            this.showLoading('Unstaging all files...');

            $.post(ajaxurl, {
                action: 'git_unstage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('All files unstaged successfully', 'success');
                    ManagePage.refreshFileStatus();
                } else {
                    ManagePage.showMessage('Error unstaging files: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Unstage all AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        viewDiff: function(file) {
            GitUtils.git.viewDiff(file);
        },

        createCommit: function(message, autoStage) {
            GitUtils.git.commit(message, autoStage, function () {
                $('#commit-message').val('');
            });
        },

        checkUnstagedChangesBeforeBranch: function(branchName) {
            this.showLoading('Checking for unstaged changes...');

            $.post(ajaxurl, {
                action: 'git_status',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    const status = response.data;
                    const hasUnstagedChanges = status.changes_count > 0;

                    if (hasUnstagedChanges) {
                        const confirmMessage = 'You have ' + status.changes_count + ' unstaged change(s) in your working directory.\n\n' +
                                             'Creating a new branch with unstaged changes may cause issues when switching between branches.\n\n' +
                                             'Would you like to:\n' +
                                             '• Click "OK" to commit these changes first, then create the branch\n' +
                                             '• Click "Cancel" to create the branch anyway (not recommended)';

                        if (confirm(confirmMessage)) {
                            // User wants to commit changes first
                            ManagePage.showCommitDialogForBranch(branchName);
                        } else {
                            // User wants to proceed anyway
                            ManagePage.createBranch(branchName);
                        }
                    } else {
                        // No unstaged changes, proceed with branch creation
                        ManagePage.createBranch(branchName);
                    }
                } else {
                    ManagePage.showMessage('Error checking git status: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Check status AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        showCommitDialogForBranch: function(branchName) {
            const commitMessage = prompt('Enter a commit message for your changes before creating the new branch:');

            if (commitMessage && commitMessage.trim()) {
                ManagePage.commitChangesAndCreateBranch(commitMessage.trim(), branchName);
            } else if (commitMessage === '') {
                alert('Please enter a commit message.');
                ManagePage.showCommitDialogForBranch(branchName);
            }
            // If user cancels (commitMessage === null), do nothing
        },

        commitChangesAndCreateBranch: function(commitMessage, branchName) {
            this.showLoading('Committing changes...');

            $.post(ajaxurl, {
                action: 'git_commit',
                message: commitMessage,
                auto_stage: true, // Auto-stage all changes
                nonce: wpGitManager.nonce
            }, function(response) {
                if (response.success) {
                    ManagePage.showMessage('Changes committed successfully', 'success');
                    // Now create the branch
                    ManagePage.createBranch(branchName);
                } else {
                    ManagePage.hideLoading();
                    ManagePage.showMessage('Error committing changes: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Commit AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        createBranch: function(branchName) {
            this.showLoading('Creating branch...');

            $.post(ajaxurl, {
                action: 'git_create_branch',
                branch_name: branchName,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Branch created successfully', 'success');
                    $('#new-branch-name').val('');
                    ManagePage.loadBranches();
                } else {
                    ManagePage.showMessage('Error creating branch: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Create branch AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadBranches: function() {
            this.showLoading('Loading branches...');

            $.post(ajaxurl, {
                action: 'git_get_branches',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayBranches(response.data);
                } else {
                    ManagePage.showMessage('Error loading branches: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load branches AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        addRemote: function(name, url) {
            this.showLoading('Adding remote...');

            $.post(ajaxurl, {
                action: 'git_add_remote',
                remote_name: name,
                remote_url: url,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Remote added successfully', 'success');
                    $('#remote-name').val('');
                    $('#remote-url').val('');
                    ManagePage.loadRemotes();
                } else {
                    ManagePage.showMessage('Error adding remote: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Add remote AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadRemotes: function() {
            this.showLoading('Loading remotes...');

            $.post(ajaxurl, {
                action: 'git_get_remotes',
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayRemotes(response.data);
                } else {
                    ManagePage.showMessage('Error loading remotes: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load remotes AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        loadHistory: function(limit) {
            this.showLoading('Loading commit history...');

            $.post(ajaxurl, {
                action: 'git_commit_history',
                limit: limit,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.displayHistory(response.data.commits);
                } else {
                    ManagePage.showMessage('Error loading history: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load history AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        executeGitCommand: function(command) {
            this.showLoading('Executing command...');

            $.post(ajaxurl, {
                action: 'git_command',
                git_command: command,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showOutput(command, response.data.message);
                } else {
                    ManagePage.showMessage('Error executing command: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        // UI helper functions (delegated to GitUtils)
        showLoading: function(message) {
            GitUtils.showLoading(message);
        },

        hideLoading: function() {
            GitUtils.hideLoading();
        },

        showMessage: function(message, type) {
            GitUtils.showMessage(message, type);
        },

        showOutput: function(command, output) {
            GitUtils.showOutput(command, output);
        },

        showDiff: function(file, diff) {
            GitUtils.showDiff(file, diff);
        },

        displayBranches: function(data) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Branch</th><th>Current</th><th>Actions</th></tr></thead><tbody>';

            if (data.branches && data.branches.length > 0) {
                data.branches.forEach(function(branch) {
                    const isCurrent = branch === data.current_branch;
                    html += '<tr>';
                    html += '<td><code>' + branch + '</code></td>';
                    html += '<td>' + (isCurrent ? '<strong>✓ Current</strong>' : '') + '</td>';
                    html += '<td>';
                    if (!isCurrent) {
                        html += '<button class="button button-small git-switch-branch" data-branch="' + branch + '">Switch</button>';
                    }
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No branches found</td></tr>';
            }

            html += '</tbody></table>';
            $('#branch-list-content').html(html);

            // Bind switch branch events
            $('.git-switch-branch').on('click', function(e) {
                e.preventDefault();
                const branch = $(this).data('branch');
                if (confirm('Switch to branch: ' + branch + '?')) {
                    ManagePage.switchBranch(branch);
                }
            });
        },

        displayRemotes: function(remotes) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Name</th><th>URL</th><th>Actions</th></tr></thead><tbody>';

            if (remotes && remotes.length > 0) {
                remotes.forEach(function(remote) {
                    html += '<tr>';
                    html += '<td><strong>' + remote.name + '</strong></td>';
                    html += '<td><code>' + remote.url + '</code></td>';
                    html += '<td><button class="button button-small button-link-delete">Remove</button></td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No remotes configured</td></tr>';
            }

            html += '</tbody></table>';
            $('#remote-list-content').html(html);
        },

        displayHistory: function(commits) {
            let html = '';

            if (commits && commits.length > 0) {
                console.log('Commits: ', commits);

                // Create table structure
                html += '<table class="wp-list-table widefat fixed striped">';
                html += '<thead>';
                html += '<tr>';
                html += '<th scope="col" class="manage-column column-hash">Hash</th>';
                html += '<th scope="col" class="manage-column column-message">Message</th>';
                html += '<th scope="col" class="manage-column column-author">Author</th>';
                html += '<th scope="col" class="manage-column column-date">Date</th>';
                html += '<th scope="col" class="manage-column column-relative">Relative</th>';
                html += '<th scope="col" class="manage-column column-actions">Actions</th>';
                html += '</tr>';
                html += '</thead>';
                html += '<tbody>';

                commits.forEach(function(commit) {
                    html += '<tr>';

                    // Handle both string format (from git log --oneline) and object format
                    if (typeof commit === 'string') {
                        // Parse string format like "f9a7a9d Initial commit"
                        const spaceIndex = commit.indexOf(' ');
                        const hash = spaceIndex > 0 ? commit.substring(0, spaceIndex) : commit.substring(0, 8);
                        const fullHash = spaceIndex > 0 ? hash : commit.substring(0, 8);
                        const message = spaceIndex > 0 ? commit.substring(spaceIndex + 1) : 'No message';

                        html += '<td><code>' + hash + '</code></td>';
                        html += '<td>' + message + '</td>';
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                        html += '<td><button type="button" class="button button-small git-view-commit-changes" data-commit="' + fullHash + '">View Changes</button></td>';
                    } else {
                        // Handle object format with hash, message, author, date properties
                        const shortHash = commit.hash.substring(0, 8);
                        const authorDisplay = commit.author + (commit.email ? ' <' + commit.email + '>' : '');

                        html += '<td><code title="' + commit.hash + '">' + shortHash + '</code></td>';
                        html += '<td>' + commit.message + '</td>';
                        html += '<td title="' + authorDisplay + '">' + commit.author + '</td>';
                        html += '<td>' + commit.date + '</td>';
                        html += '<td>' + (commit.relative_date || '-') + '</td>';
                        html += '<td><button type="button" class="button button-small git-view-commit-changes" data-commit="' + commit.hash + '">View Changes</button></td>';
                    }

                    html += '</tr>';
                });

                html += '</tbody>';
                html += '</table>';
            } else {
                html = '<p>No commits found in repository.</p>';
            }

            $('#commit-history').html(html);

            // Bind view changes events
            $('.git-view-commit-changes').on('click', function(e) {
                e.preventDefault();
                const commitHash = $(this).data('commit');
                ManagePage.viewCommitChanges(commitHash);
            });
        },

        viewCommitChanges: function(commitHash) {
            this.showLoading('Loading commit changes...');

            $.post(ajaxurl, {
                action: 'git_commit_changes',
                commit_hash: commitHash,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showCommitChanges(commitHash, response.data.changes);
                } else {
                    ManagePage.showMessage('Error loading commit changes: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Load commit changes AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        },

        showCommitChanges: function(commitHash, changes) {
            GitUtils.showCommitChanges(commitHash, changes);
        },

        switchBranch: function(branch) {
            this.showLoading('Switching branch...');

            $.post(ajaxurl, {
                action: 'git_switch_branch',
                branch: branch,
                nonce: wpGitManager.nonce
            }, function(response) {
                ManagePage.hideLoading();
                if (response.success) {
                    ManagePage.showMessage('Switched to branch: ' + branch, 'success');
                    ManagePage.loadBranches();
                } else {
                    ManagePage.showMessage('Error switching branch: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                ManagePage.hideLoading();
                console.error('Switch branch AJAX Error:', xhr.responseText);
                ManagePage.showMessage('AJAX Error: ' + error + ' (Status: ' + xhr.status + ')', 'error');
            });
        }
    };

    // Initialize ManagePage
    ManagePage.init();
});
