/**
 * Git Manager Dashboard JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const Dashboard = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            $('#refresh-status').on('click', this.handleRefreshStatus);
        },
        
        handleRefreshStatus: function() {
            var button = $(this);
            button.prop('disabled', true).text('Refreshing...');

            $.post(ajaxurl, {
                action: 'git_status',
                nonce: wpGitManager.nonce
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error refreshing status: ' + response.data);
                }
            }).always(function() {
                button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Refresh Status');
            });
        }
    };
    
    // Initialize Dashboard
    Dashboard.init();
});
