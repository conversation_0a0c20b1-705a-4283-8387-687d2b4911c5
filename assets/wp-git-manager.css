/* WP Git Manager Styles */

/* Admin Bar Styles */
#wp-admin-bar-wp-git-manager .ab-item {
    color: #fff !important;
}

#wp-admin-bar-wp-git-manager .ab-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

#wp-admin-bar-wp-git-manager .ab-submenu {
    background-color: #23282d;
}

#wp-admin-bar-wp-git-manager .ab-submenu .ab-item {
    color: #eee;
    padding: 10px 15px;
}

#wp-admin-bar-wp-git-manager .ab-submenu .ab-item:hover {
    background-color: #0073aa;
    color: #fff;
}

/* Dashboard Styles */
.dashboard-widgets-wrap {
    margin-top: 20px;
}

.dashboard-widgets-wrap .postbox-container {
    margin-right: 20px;
}

.dashboard-widgets-wrap .postbox {
    margin-bottom: 20px;
}

.dashboard-widgets-wrap .postbox-header h2 {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.dashboard-widgets-wrap .inside {
    padding: 12px;
}

/* Setup Guide Section */
.setup-guide-section {
    background: #f0f6fc;
    border: 1px solid #0969da;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.setup-guide-section h2 {
    margin-top: 0;
    color: #0969da;
}

.setup-guide-section .dashicons {
    margin-right: 5px;
}

/* Git Status Styles */
.git-status-info {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.git-file-list {
    margin: 20px 0;
}

.git-file-list h4 {
    margin: 15px 0 5px 0;
    color: #23282d;
}

.git-file-list ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.git-file-list li {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-file-added {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.git-file-modified {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.git-file-deleted {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Button Styles */
.git-stage-file,
.git-unstage-file,
.git-view-diff,
.git-view-history,
.git-branch-switch {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 5px;
}

.git-stage-file:hover,
.git-unstage-file:hover,
.git-view-diff:hover,
.git-view-history:hover,
.git-branch-switch:hover {
    background: #005a87;
}

.git-unstage-file {
    background: #dc3545;
}

.git-unstage-file:hover {
    background: #c82333;
}

.git-view-diff {
    background: #6c757d;
}

.git-view-diff:hover {
    background: #5a6268;
}

/* Modal Styles */
.git-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.git-modal {
    background: #fff;
    border-radius: 4px;
    max-width: 80%;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.git-modal-header {
    background: #f1f1f1;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-modal-header h3 {
    margin: 0;
    color: #23282d;
}

.git-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.git-modal-close:hover {
    color: #000;
}

.git-modal-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.git-modal-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Setup Page Styles */
.git-setup-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
    padding: 20px;
}

.git-setup-section h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.git-setup-form {
    display: grid;
    gap: 15px;
}

.git-setup-field {
    display: flex;
    flex-direction: column;
}

.git-setup-field label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.git-setup-field input[type="text"],
.git-setup-field input[type="email"],
.git-setup-field input[type="url"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.git-setup-field input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.git-setup-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.git-setup-btn {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.git-setup-btn:hover {
    background: #005a87;
}

.git-setup-btn.secondary {
    background: #6c757d;
}

.git-setup-btn.secondary:hover {
    background: #5a6268;
}

/* Branch Management */
.git-branches {
    margin: 20px 0;
}

.git-branches h4 {
    margin-bottom: 10px;
    color: #23282d;
}

.git-branch-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.git-branch-item {
    padding: 8px 12px;
    margin: 2px 0;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-branch-item.current {
    background: #d1ecf1;
    border-color: #bee5eb;
    font-weight: 600;
}

.git-branch-name {
    flex-grow: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .git-modal {
        max-width: 95%;
        max-height: 90%;
    }
    
    .git-modal-content {
        padding: 15px;
    }
    
    .git-file-list li {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .git-setup-actions {
        flex-direction: column;
    }
}

/* Notices */
.notice.git-notice {
    margin: 5px 0;
}

.notice.git-notice p {
    margin: 0.5em 0;
}

/* Loading States */
.git-loading {
    opacity: 0.6;
    pointer-events: none;
}

.git-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: git-spin 1s linear infinite;
}

@keyframes git-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tab Navigation Styles */
.nav-tab-wrapper {
    border-bottom: 1px solid #ccd0d4;
    margin: 0 0 20px;
    padding-top: 9px;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    border-bottom: none;
    color: #0073aa;
    display: inline-block;
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    margin: 0 5px -1px 0;
    padding: 8px 14px;
    text-decoration: none;
    text-transform: uppercase;
}

.nav-tab:hover {
    background-color: #fff;
    color: #0073aa;
}

.nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #000;
}

/* Tab Content */
.tab-content {
    margin-top: 20px;
}

.tab-pane {
    display: block;
}

/* Manage Page Styles */
.git-actions-bar {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.git-actions-bar .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.git-actions-bar .dashicons {
    margin-right: 5px;
}

/* Form Sections */
.commit-form, .branch-actions, .remote-actions, .advanced-actions {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.commit-form h3, .branch-actions h3, .remote-actions h3, .advanced-actions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

/* History Controls */
.history-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.history-controls select, .history-controls button {
    margin-left: 10px;
}

/* Commit History */
#commit-history .commit-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

#commit-history .commit-item:last-child {
    border-bottom: none;
}

.commit-hash {
    font-family: monospace;
    margin-bottom: 5px;
}

.commit-message {
    font-weight: bold;
    margin-bottom: 5px;
}

.commit-meta {
    font-size: 12px;
    color: #666;
}

/* Danger Zone */
.danger-zone {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.danger-zone h4 {
    margin-top: 0;
    color: #856404;
}

.danger-zone p {
    color: #856404;
}

.danger-zone .button {
    margin-right: 10px;
}

/* Git Command Output */
.git-command-output, .git-diff-output {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.git-command-output h4, .git-diff-output h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #495057;
}

.git-command-output pre, .git-diff-output pre {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 10px;
    overflow-x: auto;
    max-height: 400px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 12px;
    line-height: 1.4;
}

.diff-content {
    white-space: pre-wrap;
    word-wrap: break-word;
}
